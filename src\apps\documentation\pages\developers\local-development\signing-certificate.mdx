---
title: Signing Certificate
description: Learn how to create a free, self-signed certificate for local development.
---

import { Callout, Steps } from 'nextra/components';

# Create Your Signing Certificate

Digitally signing documents requires a signing certificate in `.p12` format. You can either purchase one or create a free self-signed certificate.

Follow the steps below to create a free, self-signed certificate for local development.

<Callout type="warning">
  These steps should be run on a UNIX based system, otherwise you may run into an error.
</Callout>

<Steps>

### Generate Private Key

Generate a private key using OpenSSL by running the following command:

```bash
openssl genrsa -out private.key 2048
```

This command generates a 2048-bit RSA key.

### Generate Self-Signed Certificate

Using the private key, generate a self-signed certificate by running the following command:

```bash
openssl req -new -x509 -key private.key -out certificate.crt -days 365
```

You will be prompted to enter some information, such as the certificate's Common Name (CN). Ensure that you provide the correct details. The `—days` parameter specifies the certificate's validity period.

### Create `p12` Certificate

Combine the private key and the self-signed certificate to create a `.p12` certificate. Use the following command:

```bash
openssl pkcs12 -export -out certificate.p12 -inkey private.key -in certificate.crt -legacy
```

<Callout type="warning">
When running the application in Docker, you may encounter permission issues when attempting to sign documents using your certificate (.p12) file. This happens because the application runs as a non-root user inside the container and needs read access to the certificate.

To resolve this, you'll need to update the certificate file permissions to allow the container user 1001, which runs NextJS, to read it:

```bash
sudo chown 1001 certificate.p12
```

</Callout>

### `p12` Certificate Password

When you create the `.p12` certificate, you will be prompted to enter a password. Enter a strong password and keep it secure. Remember this password, as it will be required when using the certificate.

Note that for local development, the password can be left empty.

### Add Certificate to the Project

Use the `NEXT_PRIVATE_SIGNING_LOCAL_FILE_PATH` environment variable to point at the certificate you created.

Details about environment variables associated with certificates can be found [here](/developers/self-hosting/signing-certificate#configure-documenso-to-use-the-certificate).

</Steps>
