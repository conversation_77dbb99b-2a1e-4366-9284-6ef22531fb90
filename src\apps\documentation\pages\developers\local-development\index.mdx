---
title: Local Development
description: Learn how to set up Documenso for local development.
---

# Local development

There are multiple ways of setting up Documenso for local development. At the moment of writing this documentation, there are 3 ways of running Documenso locally:

- [Using the developer quickstart with Dock<PERSON>](/developers/local-development/quickstart)
- [Manually setting up the development environment](/developers/local-development/manual)
- [Using Gitpod](/developers/local-development/gitpod)

Pick the one that fits your needs the best.

## Tech Stack

- [Typescript](https://www.typescriptlang.org/) - Language
- [React Router](https://reactrouter.com/) - Framework
- [Prisma](https://www.prisma.io/) - ORM
- [Tailwind](https://tailwindcss.com/) - CSS
- [shadcn/ui](https://ui.shadcn.com/) - Component Library
- [react-email](https://react.email/) - Email Templates
- [tRPC](https://trpc.io/) - API
- [@documenso/pdf-sign](https://www.npmjs.com/package/@documenso/pdf-sign) - PDF Signatures
- [React-PDF](https://github.com/wojtekmaj/react-pdf) - Viewing PDFs
- [PDF-Lib](https://github.com/Hopding/pdf-lib) - PDF manipulation
- [Stripe](https://stripe.com/) - Payments

<div className="mt-16 flex items-center justify-center gap-4">
  <a href="https://documen.so/discord">
    <img
      src="https://img.shields.io/badge/Discord-documen.so/discord-%235865F2"
      alt="Join Documenso on Discord"
    />
  </a>
  <a href="https://github.com/documenso/documenso/stargazers">
    <img src="https://img.shields.io/github/stars/documenso/documenso" alt="Github Stars" />
  </a>
  <a href="https://github.com/documenso/documenso/blob/main/LICENSE">
    <img src="https://img.shields.io/badge/license-AGPLv3-purple" alt="License" />
  </a>
  <a href="https://github.com/documenso/documenso/pulse">
    <img
      src="https://img.shields.io/github/commit-activity/m/documenso/documenso"
      alt="Commits-per-month"
    />
  </a>
  <a href="https://vscode.dev/redirect?url=vscode://ms-vscode-remote.remote-containers/cloneInVolume?url=https://github.com/documenso/documenso">
    <img
      alt="open in devcontainer"
      src="https://img.shields.io/static/v1?label=Dev%20Containers&message=Enabled&color=blue&logo=visualstudiocode"
    />
  </a>
  <a href="https://github.com/documenso/documenso/blob/main/CODE_OF_CONDUCT.md">
    <img
      src="https://img.shields.io/badge/Contributor%20Covenant-2.1-4baaaa.svg"
      alt="Contributor Covenant"
    />
  </a>
</div>
