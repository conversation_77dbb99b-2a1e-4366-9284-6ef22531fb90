---
title: Webhooks
description: Learn how to use webhooks to receive real-time notifications about your documents.
---

# Webhooks

Webhooks are HTTP callbacks triggered by specific events. When the user subscribes to a specific event, and that event occurs, the webhook makes an HTTP request to the URL provided by the user. The request can be a simple notification or carry a payload with more information about the event.

Some of the common use cases for webhooks include:

1. **Real-time Data Syncing**: Webhooks provide a way to keep data in sync across different platforms. For example, you can keep your system up-to-date with your Documenso documents by subscribing to events like document creation or signing.
2. **Automating Workflows**: They can trigger automated workflows that start when an event occurs. For example, the webhook could trigger an email when a document is signed.
3. **Integrating Third-Party Services**: Webhooks can be used to integrate Documenso with third-party services. For example, you could use a webhook to send data to a CRM system when a document is signed.

Documenso supports Webhooks and allows you to subscribe to the following events:

- `document.created`
- `document.sent`
- `document.opened`
- `document.signed`
- `document.completed`
- `document.rejected`
- `document.cancelled`

## Create a webhook subscription

You can create a webhook subscription from the user settings page. Click on your avatar in the top right corner of the dashboard and select "**[User settings](https://app.documenso.com/settings)**" from the dropdown menu.

![A screenshot of the Documenso's dashboard that shows the dropdown menu when you click on your user avatar](/webhook-images/dashboard-user-dropdown-menu.webp)

Then, navigate to the "**[Webhooks](https://app.documenso.com/settings/webhooks)**" tab, where you can see a list of your existing webhooks and create new ones.

![A screenshot of the Documenso's user settings page that shows the Webhooks tab and the Create Webhook button](/webhook-images/webhooks-settings-page.webp)

Clicking on the "**Create Webhook**" button opens a modal to create a new webhook subscription.

To create a new webhook subscription, you need to provide the following information:

- Enter the webhook URL that will receive the event payload.
- Select the event(s) you want to subscribe to: `document.created`, `document.sent`, `document.opened`, `document.signed`, `document.completed`, `document.rejected`, `document.cancelled`.
- Optionally, you can provide a secret key that will be used to sign the payload. This key will be included in the `X-Documenso-Secret` header of the request.

![A screenshot of the Create Webhook modal that shows the URL input field and the event checkboxes](/webhook-images/webhooks-page-create-webhook-modal.webp)

After you have filled in the required information, click on the "**Create Webhook**" button to save your subscription.

The screenshot below illustrates a newly created webhook subscription.

![A screenshot of the Documenso's user settings page that shows the newly created webhook subscription](/webhook-images/webhooks-page.webp)

You can edit or delete your webhook subscriptions by clicking the "**Edit**" or "**Delete**" buttons next to the webhook.

## Webhook fields

The payload sent to the webhook URL contains the following fields:

| Field                                        | Type      | Description                                           |
| -------------------------------------------- | --------- | ----------------------------------------------------- |
| `event`                                      | string    | The type of event that triggered the webhook.         |
| `payload.id`                                 | number    | The id of the document.                               |
| `payload.externalId`                         | string?   | External identifier for the document.                 |
| `payload.userId`                             | number    | The id of the user who owns the document.             |
| `payload.authOptions`                        | json?     | Authentication options for the document.              |
| `payload.formValues`                         | json?     | Form values for the document.                         |
| `payload.visibility`                         | string    | Document visibility (e.g., EVERYONE).                 |
| `payload.title`                              | string    | The title of the document.                            |
| `payload.status`                             | string    | The current status of the document.                   |
| `payload.documentDataId`                     | string    | The identifier for the document data.                 |
| `payload.createdAt`                          | datetime  | The creation date and time of the document.           |
| `payload.updatedAt`                          | datetime  | The last update date and time of the document.        |
| `payload.completedAt`                        | datetime? | The completion date and time of the document.         |
| `payload.deletedAt`                          | datetime? | The deletion date and time of the document.           |
| `payload.teamId`                             | number?   | The id of the team if document belongs to a team.     |
| `payload.templateId`                         | number?   | The id of the template if created from template.      |
| `payload.source`                             | string    | The source of the document (e.g., DOCUMENT, TEMPLATE) |
| `payload.documentMeta.id`                    | string    | The id of the document metadata.                      |
| `payload.documentMeta.subject`               | string?   | The subject of the document.                          |
| `payload.documentMeta.message`               | string?   | The message associated with the document.             |
| `payload.documentMeta.timezone`              | string    | The timezone setting for the document.                |
| `payload.documentMeta.password`              | string?   | The password protection if set.                       |
| `payload.documentMeta.dateFormat`            | string    | The date format used in the document.                 |
| `payload.documentMeta.redirectUrl`           | string?   | The URL to redirect after signing.                    |
| `payload.documentMeta.signingOrder`          | string    | The signing order (e.g., PARALLEL, SEQUENTIAL).       |
| `payload.documentMeta.typedSignatureEnabled` | boolean   | Whether typed signatures are enabled.                 |
| `payload.documentMeta.language`              | string    | The language of the document.                         |
| `payload.documentMeta.distributionMethod`    | string    | The method of distributing the document.              |
| `payload.documentMeta.emailSettings`         | json?     | Email notification settings.                          |
| `payload.Recipient[].id`                     | number    | The id of the recipient.                              |
| `payload.Recipient[].documentId`             | number?   | The id of the document for this recipient.            |
| `payload.Recipient[].templateId`             | number?   | The template id if from a template.                   |
| `payload.Recipient[].email`                  | string    | The email address of the recipient.                   |
| `payload.Recipient[].name`                   | string    | The name of the recipient.                            |
| `payload.Recipient[].token`                  | string    | The unique token for this recipient.                  |
| `payload.Recipient[].documentDeletedAt`      | datetime? | When the document was deleted for this recipient.     |
| `payload.Recipient[].expired`                | datetime? | When the recipient's access expired.                  |
| `payload.Recipient[].signedAt`               | datetime? | When the recipient signed the document.               |
| `payload.Recipient[].authOptions`            | json?     | Authentication options for this recipient.            |
| `payload.Recipient[].signingOrder`           | number?   | The order in which this recipient should sign.        |
| `payload.Recipient[].rejectionReason`        | string?   | The reason if the recipient rejected the document.    |
| `payload.Recipient[].role`                   | string    | The role of the recipient (e.g., SIGNER, VIEWER).     |
| `payload.Recipient[].readStatus`             | string    | Whether the recipient has read the document.          |
| `payload.Recipient[].signingStatus`          | string    | The signing status of this recipient.                 |
| `payload.Recipient[].sendStatus`             | string    | The sending status for this recipient.                |
| `createdAt`                                  | datetime  | The creation date and time of the webhook event.      |
| `webhookEndpoint`                            | string    | The endpoint URL where the webhook is sent.           |

## Example payloads

Below are examples of the payloads that are sent for each of the supported events. The payloads are sent as JSON data in the body of the POST request.

Example payload for the `document.created` event:

```json
{
  "event": "DOCUMENT_CREATED",
  "payload": {
    "id": 10,
    "externalId": null,
    "userId": 1,
    "authOptions": null,
    "formValues": null,
    "visibility": "EVERYONE",
    "title": "documenso.pdf",
    "status": "DRAFT",
    "documentDataId": "hs8qz1ktr9204jn7mg6c5dxy0",
    "createdAt": "2024-04-22T11:44:43.341Z",
    "updatedAt": "2024-04-22T11:44:43.341Z",
    "completedAt": null,
    "deletedAt": null,
    "teamId": null,
    "templateId": null,
    "source": "DOCUMENT",
    "documentMeta": {
      "id": "doc_meta_123",
      "subject": "Please sign this document",
      "message": "Hello, please review and sign this document.",
      "timezone": "UTC",
      "password": null,
      "dateFormat": "MM/DD/YYYY",
      "redirectUrl": null,
      "signingOrder": "PARALLEL",
      "typedSignatureEnabled": true,
      "language": "en",
      "distributionMethod": "EMAIL",
      "emailSettings": null
    },
    "Recipient": [
      {
        "id": 52,
        "documentId": 10,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "John Doe",
        "token": "vbT8hi3jKQmrFP_LN1WcS",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": null,
        "authOptions": null,
        "signingOrder": 1,
        "rejectionReason": null,
        "role": "SIGNER",
        "readStatus": "NOT_OPENED",
        "signingStatus": "NOT_SIGNED",
        "sendStatus": "NOT_SENT"
      }
    ]
  },
  "createdAt": "2024-04-22T11:44:44.779Z",
  "webhookEndpoint": "https://mywebhooksite.com/mywebhook"
}
```

Example payload for the `document.sent` event:

```json
{
  "event": "DOCUMENT_SENT",
  "payload": {
    "id": 10,
    "externalId": null,
    "userId": 1,
    "authOptions": null,
    "formValues": null,
    "visibility": "EVERYONE",
    "title": "documenso.pdf",
    "status": "PENDING",
    "documentDataId": "hs8qz1ktr9204jn7mg6c5dxy0",
    "createdAt": "2024-04-22T11:44:43.341Z",
    "updatedAt": "2024-04-22T11:48:07.569Z",
    "completedAt": null,
    "deletedAt": null,
    "teamId": null,
    "templateId": null,
    "source": "DOCUMENT",
    "documentMeta": {
      "id": "doc_meta_123",
      "subject": "Please sign this document",
      "message": "Hello, please review and sign this document.",
      "timezone": "UTC",
      "password": null,
      "dateFormat": "MM/DD/YYYY",
      "redirectUrl": null,
      "signingOrder": "PARALLEL",
      "typedSignatureEnabled": true,
      "language": "en",
      "distributionMethod": "EMAIL",
      "emailSettings": null
    },
    "Recipient": [
      {
        "id": 52,
        "documentId": 10,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "Signer 2",
        "token": "vbT8hi3jKQmrFP_LN1WcS",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": null,
        "authOptions": null,
        "signingOrder": 1,
        "rejectionReason": null,
        "role": "VIEWER",
        "readStatus": "NOT_OPENED",
        "signingStatus": "NOT_SIGNED",
        "sendStatus": "SENT"
      },
      {
        "id": 53,
        "documentId": 10,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "Signer 1",
        "token": "HkrptwS42ZBXdRKj1TyUo",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": null,
        "authOptions": null,
        "signingOrder": 2,
        "rejectionReason": null,
        "role": "SIGNER",
        "readStatus": "NOT_OPENED",
        "signingStatus": "NOT_SIGNED",
        "sendStatus": "SENT"
      }
    ]
  },
  "createdAt": "2024-04-22T11:48:07.945Z",
  "webhookEndpoint": "https://mywebhooksite.com/mywebhook"
}
```

Example payload for the `document.opened` event:

```json
{
  "event": "DOCUMENT_OPENED",
  "payload": {
    "id": 10,
    "externalId": null,
    "userId": 1,
    "authOptions": null,
    "formValues": null,
    "visibility": "EVERYONE",
    "title": "documenso.pdf",
    "status": "PENDING",
    "documentDataId": "hs8qz1ktr9204jn7mg6c5dxy0",
    "createdAt": "2024-04-22T11:44:43.341Z",
    "updatedAt": "2024-04-22T11:48:07.569Z",
    "completedAt": null,
    "deletedAt": null,
    "teamId": null,
    "templateId": null,
    "source": "DOCUMENT",
    "documentMeta": {
      "id": "doc_meta_123",
      "subject": "Please sign this document",
      "message": "Hello, please review and sign this document.",
      "timezone": "UTC",
      "password": null,
      "dateFormat": "MM/DD/YYYY",
      "redirectUrl": null,
      "signingOrder": "PARALLEL",
      "typedSignatureEnabled": true,
      "language": "en",
      "distributionMethod": "EMAIL",
      "emailSettings": null
    },
    "Recipient": [
      {
        "id": 52,
        "documentId": 10,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "Signer 2",
        "token": "vbT8hi3jKQmrFP_LN1WcS",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": null,
        "authOptions": null,
        "signingOrder": 1,
        "rejectionReason": null,
        "role": "VIEWER",
        "readStatus": "OPENED",
        "signingStatus": "NOT_SIGNED",
        "sendStatus": "SENT"
      }
    ]
  },
  "createdAt": "2024-04-22T11:50:26.174Z",
  "webhookEndpoint": "https://mywebhooksite.com/mywebhook"
}
```

Example payload for the `document.signed` event:

```json
{
  "event": "DOCUMENT_SIGNED",
  "payload": {
    "id": 10,
    "externalId": null,
    "userId": 1,
    "authOptions": null,
    "formValues": null,
    "visibility": "EVERYONE",
    "title": "documenso.pdf",
    "status": "COMPLETED",
    "documentDataId": "hs8qz1ktr9204jn7mg6c5dxy0",
    "createdAt": "2024-04-22T11:44:43.341Z",
    "updatedAt": "2024-04-22T11:52:05.708Z",
    "completedAt": "2024-04-22T11:52:05.707Z",
    "deletedAt": null,
    "teamId": null,
    "templateId": null,
    "source": "DOCUMENT",
    "documentMeta": {
      "id": "doc_meta_123",
      "subject": "Please sign this document",
      "message": "Hello, please review and sign this document.",
      "timezone": "UTC",
      "password": null,
      "dateFormat": "MM/DD/YYYY",
      "redirectUrl": null,
      "signingOrder": "PARALLEL",
      "typedSignatureEnabled": true,
      "language": "en",
      "distributionMethod": "EMAIL",
      "emailSettings": null
    },
    "Recipient": [
      {
        "id": 51,
        "documentId": 10,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "Signer 1",
        "token": "HkrptwS42ZBXdRKj1TyUo",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": "2024-04-22T11:52:05.688Z",
        "authOptions": {
          "accessAuth": null,
          "actionAuth": null
        },
        "signingOrder": 1,
        "rejectionReason": null,
        "role": "SIGNER",
        "readStatus": "OPENED",
        "signingStatus": "SIGNED",
        "sendStatus": "SENT"
      }
    ]
  },
  "createdAt": "2024-04-22T11:52:18.577Z",
  "webhookEndpoint": "https://mywebhooksite.com/mywebhook"
}
```

Example payload for the `document.completed` event:

```json
{
  "event": "DOCUMENT_COMPLETED",
  "payload": {
    "id": 10,
    "externalId": null,
    "userId": 1,
    "authOptions": null,
    "formValues": null,
    "visibility": "EVERYONE",
    "title": "documenso.pdf",
    "status": "COMPLETED",
    "documentDataId": "hs8qz1ktr9204jn7mg6c5dxy0",
    "createdAt": "2024-04-22T11:44:43.341Z",
    "updatedAt": "2024-04-22T11:52:05.708Z",
    "completedAt": "2024-04-22T11:52:05.707Z",
    "deletedAt": null,
    "teamId": null,
    "templateId": null,
    "source": "DOCUMENT",
    "documentMeta": {
      "id": "doc_meta_123",
      "subject": "Please sign this document",
      "message": "Hello, please review and sign this document.",
      "timezone": "UTC",
      "password": null,
      "dateFormat": "MM/DD/YYYY",
      "redirectUrl": null,
      "signingOrder": "PARALLEL",
      "typedSignatureEnabled": true,
      "language": "en",
      "distributionMethod": "EMAIL",
      "emailSettings": null
    },
    "Recipient": [
      {
        "id": 50,
        "documentId": 10,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "Signer 2",
        "token": "vbT8hi3jKQmrFP_LN1WcS",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": "2024-04-22T11:51:10.055Z",
        "authOptions": {
          "accessAuth": null,
          "actionAuth": null
        },
        "signingOrder": 1,
        "rejectionReason": null,
        "role": "VIEWER",
        "readStatus": "OPENED",
        "signingStatus": "SIGNED",
        "sendStatus": "SENT"
      },
      {
        "id": 51,
        "documentId": 10,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "Signer 1",
        "token": "HkrptwS42ZBXdRKj1TyUo",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": "2024-04-22T11:52:05.688Z",
        "authOptions": {
          "accessAuth": null,
          "actionAuth": null
        },
        "signingOrder": 2,
        "rejectionReason": null,
        "role": "SIGNER",
        "readStatus": "OPENED",
        "signingStatus": "SIGNED",
        "sendStatus": "SENT"
      }
    ]
  },
  "createdAt": "2024-04-22T11:52:18.277Z",
  "webhookEndpoint": "https://mywebhooksite.com/mywebhook"
}
```

Example payload for the `document.rejected` event:

```json
{
  "event": "DOCUMENT_REJECTED",
  "payload": {
    "id": 10,
    "externalId": null,
    "userId": 1,
    "authOptions": null,
    "formValues": null,
    "visibility": "EVERYONE",
    "title": "documenso.pdf",
    "status": "PENDING",
    "documentDataId": "hs8qz1ktr9204jn7mg6c5dxy0",
    "createdAt": "2024-04-22T11:44:43.341Z",
    "updatedAt": "2024-04-22T11:48:07.569Z",
    "completedAt": null,
    "deletedAt": null,
    "teamId": null,
    "templateId": null,
    "source": "DOCUMENT",
    "documentMeta": {
      "id": "doc_meta_123",
      "subject": "Please sign this document",
      "message": "Hello, please review and sign this document.",
      "timezone": "UTC",
      "password": null,
      "dateFormat": "MM/DD/YYYY",
      "redirectUrl": null,
      "signingOrder": "PARALLEL",
      "typedSignatureEnabled": true,
      "language": "en",
      "distributionMethod": "EMAIL",
      "emailSettings": null
    },
    "Recipient": [
      {
        "id": 52,
        "documentId": 10,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "Signer",
        "token": "vbT8hi3jKQmrFP_LN1WcS",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": "2024-04-22T11:48:07.569Z",
        "authOptions": {
          "accessAuth": null,
          "actionAuth": null
        },
        "signingOrder": 1,
        "rejectionReason": "I do not agree with the terms",
        "role": "SIGNER",
        "readStatus": "OPENED",
        "signingStatus": "REJECTED",
        "sendStatus": "SENT"
      }
    ]
  },
  "createdAt": "2024-04-22T11:48:07.945Z",
  "webhookEndpoint": "https://mywebhooksite.com/mywebhook"
}
```

Example payload for the `document.rejected` event:

```json
{
  "event": "DOCUMENT_CANCELLED",
  "payload": {
    "id": 7,
    "externalId": null,
    "userId": 3,
    "authOptions": null,
    "formValues": null,
    "visibility": "EVERYONE",
    "title": "documenso.pdf",
    "status": "PENDING",
    "documentDataId": "cm6exvn93006hi02ru90a265a",
    "createdAt": "2025-01-27T11:02:14.393Z",
    "updatedAt": "2025-01-27T11:03:16.387Z",
    "completedAt": null,
    "deletedAt": null,
    "teamId": null,
    "templateId": null,
    "source": "DOCUMENT",
    "documentMeta": {
      "id": "cm6exvn96006ji02rqvzjvwoy",
      "subject": "",
      "message": "",
      "timezone": "Etc/UTC",
      "password": null,
      "dateFormat": "yyyy-MM-dd hh:mm a",
      "redirectUrl": "",
      "signingOrder": "PARALLEL",
      "typedSignatureEnabled": true,
      "language": "en",
      "distributionMethod": "EMAIL",
      "emailSettings": {
        "documentDeleted": true,
        "documentPending": true,
        "recipientSigned": true,
        "recipientRemoved": true,
        "documentCompleted": true,
        "ownerDocumentCompleted": true,
        "recipientSigningRequest": true
      }
    },
    "recipients": [
      {
        "id": 7,
        "documentId": 7,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "Zorita Baird",
        "token": "XkKx1HCs6Znm2UBJA2j6o",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": null,
        "authOptions": { "accessAuth": null, "actionAuth": null },
        "signingOrder": 1,
        "rejectionReason": null,
        "role": "SIGNER",
        "readStatus": "NOT_OPENED",
        "signingStatus": "NOT_SIGNED",
        "sendStatus": "SENT"
      }
    ],
    "Recipient": [
      {
        "id": 7,
        "documentId": 7,
        "templateId": null,
        "email": "<EMAIL>",
        "name": "Signer",
        "token": "XkKx1HCs6Znm2UBJA2j6o",
        "documentDeletedAt": null,
        "expired": null,
        "signedAt": null,
        "authOptions": { "accessAuth": null, "actionAuth": null },
        "signingOrder": 1,
        "rejectionReason": null,
        "role": "SIGNER",
        "readStatus": "NOT_OPENED",
        "signingStatus": "NOT_SIGNED",
        "sendStatus": "SENT"
      }
    ]
  },
  "createdAt": "2025-01-27T11:03:27.730Z",
  "webhookEndpoint": "https://mywebhooksite.com/mywebhook"
}
```

## Availability

Webhooks are available to individual users and teams.
