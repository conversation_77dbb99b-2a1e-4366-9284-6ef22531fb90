name: 'Validate PR Name'

on:
  pull_request_target:
    types:
      - opened
      - reopened
      - edited
      - synchronize

permissions:
  pull-requests: write

jobs:
  validate-pr:
    name: Validate PR title
    runs-on: ubuntu-latest
    steps:
      - name: Check PR creator's previous activity
        id: check_activity
        run: |
          CREATOR=$(curl -s "https://api.github.com/repos/${{ github.repository }}/pulls/${{ github.event.pull_request.number }}" | jq -r '.user.login')
          ACTIVITY=$(curl -s "https://api.github.com/search/commits?q=author:${CREATOR}+repo:${{ github.repository }}" | jq -r '.total_count')
          if [ "$ACTIVITY" -eq 0 ]; then
            echo "::set-output name=is_new::true"
          else
            echo "::set-output name=is_new::false"
          fi

      - name: Count PRs created by user
        id: count_prs
        run: |
          CREATOR=$(curl -s "https://api.github.com/repos/${{ github.repository }}/pulls/${{ github.event.pull_request.number }}" | jq -r '.user.login')
          PR_COUNT=$(curl -s "https://api.github.com/search/issues?q=type:pr+is:open+author:${CREATOR}+repo:${{ github.repository }}" | jq -r '.total_count')
          echo "::set-output name=pr_count::$PR_COUNT"

      - uses: amannn/action-semantic-pull-request@v5
        id: lint_pr_title
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - uses: marocchino/sticky-pull-request-comment@v2
        if: always() && (steps.lint_pr_title.outputs.error_message != null)
        with:
          header: pr-title-lint-error
          message: |
            Hey There! and thank you for opening this pull request! 📝👋🏼

            We require pull request titles to follow the [Conventional Commits Spec](https://www.conventionalcommits.org/en/v1.0.0/) and it looks like your proposed title needs to be adjusted.

            Details:

            ```
            ${{ steps.lint_pr_title.outputs.error_message }}
            ```

      - if: ${{ steps.lint_pr_title.outputs.error_message == null && steps.check_activity.outputs.is_new == 'false' && steps.count_prs.outputs.pr_count < 2}}
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: pr-title-lint-error
          message: |
            Thank you for following the naming conventions for pull request titles! 💚🚀
