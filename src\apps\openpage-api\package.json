{"name": "@documenso/openpage-api", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "next build", "start": "next start", "lint:fix": "next lint --fix", "clean": "rimraf .next && rimraf node_modules"}, "dependencies": {"@documenso/prisma": "*", "luxon": "^3.5.0", "next": "14.2.28"}, "devDependencies": {"@types/node": "^20", "@types/react": "18.3.5", "typescript": "5.6.2"}}