# This is similar to the "Pull Translations" workflow, but without the conditional check to allow us to
# forcefully pull down translations from Crowdin and create a PR regardless if all the translations are fulfilled.
#
# Intended to be used when we manually update translations in Crowdin UI and want to pull those down when
# they already exist.

name: 'Force pull translations'

on:
  workflow_dispatch:
  workflow_call:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  pull_translations:
    name: Force pull translations
    runs-on: ubuntu-latest
    environment: Translations
    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: ./.github/actions/node-install

      - name: Pull translations from Crowdin
        uses: crowdin/github-action@v2
        with:
          upload_sources: false
          upload_translations: false
          download_translations: true
          export_only_approved: false
          localization_branch_name: chore/translations
          commit_message: 'chore: add translations'
          pull_request_title: 'chore: add translations'

        env:
          # A classic GitHub Personal Access Token with the 'repo' scope selected (the user should have write access to the repository).
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}

          # A numeric ID, found at https://crowdin.com/project/<projectName>/tools/api
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}

          # Visit https://crowdin.com/settings#api-key to create this token
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
