---
title: Send a document for signing
description: The guide gives a detailed description of all options available when sending out a document for signing.
---

import { Callout, Steps } from 'nextra/components';

# Send Documents for Signing

This guide will walk you through the process of sending a document out for signing. You will learn how to upload a document, add recipients, place signature fields, and send the document for signing.

<Steps>
### Login Into Your Account

The guide assumes you have a Documenso account. If you don't, you can create a free account [here](https://documen.so/free-docs).

### Upload Document

Navigate to the [Documenso dashboard](https://app.documenso.com/documents) and click on the "Add a document" button. Select the document you want to upload and wait for the upload to complete.

![Documenso dashboard](/document-signing/documenso-documents-dashboard.webp)

After the upload is complete, you will be redirected to the document's page. You can configure the document's settings and add recipients and fields here.

![Documenso document overview](/document-signing/documenso-uploaded-document.webp)

### (Optional) Advanced Options

Click on the "Advanced options" button to access additional settings for the document. You can set an external ID, date format, time zone, and the redirect URL.

![Advanced settings for a document uploaded in the Documenso dashboard](/document-signing/documenso-uploaded-document-advanced-options.webp)

The external ID allows you to set a custom ID for the document that can be used to identify the document in your external system(s).

The date format and time zone settings allow you to customize how the date and time are displayed in the document.

The redirect URL allows you to specify a URL where the signer will be redirected after signing the document.

### (Optional) Document Access

Documenso enables you to set up access control for your documents. You can require authentication for viewing the document.

The available options are:

- **Require account** - The recipient must be signed in to view the document.
- **None** - The document can be accessed directly by the URL sent to the recipient.

![Document access settings in the Documenso dashboard](/document-signing/documenso-enterprise-document-access.webp)

<Callout type="info">
  The "Document Access" feature is only available for Enterprise accounts.
</Callout>

### (Optional) Recipient Authentication

The "Recipient Authentication" feature allows you to specify the authentication method required for recipients to sign the signature field.

The available options are:

- **Require passkey** - The recipient must have an account and passkey configured via their settings.
- **Require 2FA** - The recipient must have an account and 2FA enabled via their settings.
- **None** - No authentication required.

![Document recipient action authentication in the Documenso dashboard](/document-signing/document-enterprise-recipient-action-authentication.webp)

This can be overridden by setting the authentication requirements directly for each recipient in the next step.

<Callout type="info">
  The "Recipient Authentication" feature is only available for Enterprise accounts.
</Callout>

### Recipients

Click the "+ Add Signer" button to add a new recipient. You can configure the recipient's email address, name, role, and authentication method on this page.

You can choose any option from the ["Recipient Authentication"](#optional-recipient-authentication) section, or you can set it to "Inherit authentication method" to use the global action signing authentication method configured in the "General Settings" step.

![The required authentication method for a recipient](/document-signing/documenso-document-recipient-authentication-method.webp)

You can also set the recipient's role, which determines their actions and permissions in the document.

![The recipient role](/document-signing/documenso-document-recipient-role.webp)

#### Roles

Documenso has 4 roles for recipients with different permissions and actions.

|   Role    |                                    Function                                     | Action required | Signature |
| :-------: | :-----------------------------------------------------------------------------: | :-------------: | :-------: |
|  Signer   |                Needs to sign signatures fields assigned to them.                |       Yes       |    Yes    |
| Approver  |          Needs to approve the document as a whole. Signature optional.          |       Yes       | Optional  |
|  Viewer   |                   Needs to confirm they viewed the document.                    |       Yes       |    No     |
| Assistant | Can help prepare the document by filling in fields on behalf of other signers.  |       Yes       |    No     |
|    CC     | Receives a copy of the signed document after completion. No action is required. |       No        |    No     |

### Fields

Documenso supports 9 different field types that can be added to the document. Each field type collects various information from the recipients when they sign the document.

![The available field types in the Documenso dashboard](/document-signing/documenso-document-fields.webp)

The available field types are:

- **Signature** - Collects the signer's signature
- **Email** - Collects the signer's email address
- **Name** - Collects the signer's name
- **Date** - Collects the date of the signature
- **Text** - Collects text input from the signer
- **Number** - Collects a number input from the signer
- **Radio** - Collects a single choice from the signer
- **Checkbox** - Collects multiple choices from the signer
- **Dropdown/Select** - Collects a single choice from a list of choices

All fields can be placed anywhere on the document and resized as needed.

<Callout type="info">
  Learn more about the available field types and how to use them on the [Fields
  page](signing-documents/fields).
</Callout>

#### Signature Required

Signer Roles require at least 1 signature field. You will get an error message if you try to send a document without a signature field.

![Error message when trying to send a document without a signature field](/document-signing/documenso-no-signature-field-found.webp)

### Email Settings

Before sending the document, you can configure the email settings and customize the subject line, message, and sender name.

![Email settings in the Documenso dashboard](/document-signing/documenso-document-email-settings.webp)

If you leave the email subject and message empty, Documenso will use the default email template.

### Sending the Document

After configuring the document, click the "Send" button to send the document to the recipients. The recipients will receive an email with a link to sign the document.

![The email sent to the recipients with the signing link](/document-signing/documenso-sign-email.webp)

#### Signing Link

If you need to copy the signing link for each recipient, you can do so by clicking on the recipient whose link you want to copy. The signing link is copied automatically to your clipboard.

![How to copy the signing link for each recipient from the Documenso dashboard](/document-signing/documenso-signing-links.webp)

The signing link has the following format:

```bash
https://app.documenso.com/sign/12ACP777zxQLO52hjj_vCB
```

</Steps>
