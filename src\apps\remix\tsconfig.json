{"include": ["**/*", "**/.server/**/*", "**/.client/**/*", ".react-router/types/**/*"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["node", "vite/client"], "target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "jsx": "react-jsx", "rootDirs": [".", "./.react-router/types"], "baseUrl": ".", "paths": {"~/*": ["./app/*"], "@documenso/api": ["../../packages/api"], "@documenso/assets": ["../../packages/assets"], "@documenso/auth": ["../../packages/auth"], "@documenso/ee": ["../../packages/ee"], "@documenso/lib": ["../../packages/lib"], "@documenso/prisma": ["../../packages/prisma"], "@documenso/trpc": ["../../packages/trpc"], "@documenso/ui": ["../../packages/ui"], "@documenso/tailwind-config": ["../../packages/tailwind-config"]}, "esModuleInterop": true, "verbatimModuleSyntax": true, "noEmit": true, "moduleDetection": "force", "resolveJsonModule": true, "isolatedModules": true, "skipLibCheck": true, "strict": true, "useUnknownInCatchVariables": false}}