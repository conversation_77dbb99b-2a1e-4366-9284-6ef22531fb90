{"name": "@documenso/remix", "private": true, "type": "module", "scripts": {"build": "./.bin/build.sh", "build:app": "npm run typecheck && cross-env NODE_ENV=production react-router build", "build:server": "cross-env NODE_ENV=production rollup -c rollup.config.mjs", "dev": "npm run with:env -- react-router dev", "dev:billing": "bash .bin/stripe-dev.sh", "start": "npm run with:env -- cross-env NODE_ENV=production node build/server/main.js", "clean": "rimraf .react-router && rimraf node_modules", "typecheck": "react-router typegen && tsc", "with:env": "dotenv -e ../../.env -e ../../.env.local --"}, "dependencies": {"@documenso/api": "*", "@documenso/assets": "*", "@documenso/auth": "*", "@documenso/ee": "*", "@documenso/lib": "*", "@documenso/prisma": "*", "@documenso/tailwind-config": "*", "@documenso/trpc": "*", "@documenso/ui": "*", "@epic-web/remember": "^1.1.0", "@hono/node-server": "^1.13.7", "@hono/trpc-server": "^0.3.4", "@hookform/resolvers": "^3.1.0", "@lingui/core": "^5.2.0", "@lingui/detect-locale": "^5.2.0", "@lingui/macro": "^5.2.0", "@lingui/react": "^5.2.0", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@react-router/node": "^7.6.0", "@react-router/serve": "^7.6.0", "@simplewebauthn/browser": "^9.0.1", "@simplewebauthn/server": "^9.0.3", "autoprefixer": "^10.4.13", "colord": "^2.9.3", "framer-motion": "^10.12.8", "hono": "4.7.0", "hono-react-router-adapter": "^0.6.2", "input-otp": "^1.2.4", "isbot": "^5.1.17", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.279.0", "luxon": "^3.4.0", "papaparse": "^5.4.1", "plausible-tracker": "^0.3.9", "posthog-js": "^1.245.0", "posthog-node": "^4.17.0", "react": "^18", "react-call": "^1.3.0", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.43.9", "react-hotkeys-hook": "^4.4.1", "react-icons": "^5.4.0", "react-rnd": "^10.4.1", "react-router": "^7.6.0", "recharts": "^2.7.2", "remeda": "^2.17.3", "remix-themes": "^2.0.4", "satori": "^0.12.1", "sharp": "0.32.6", "tailwindcss": "^3.4.15", "ts-pattern": "^5.0.5", "ua-parser-js": "^1.0.37", "uqr": "^0.1.2"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@lingui/babel-plugin-lingui-macro": "^5.2.0", "@lingui/vite-plugin": "^5.3.1", "@react-router/dev": "^7.6.0", "@react-router/remix-routes-option-adapter": "^7.6.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.2", "@simplewebauthn/types": "^9.0.1", "@types/formidable": "^2.0.6", "@types/luxon": "^3.3.1", "@types/node": "^20", "@types/papaparse": "^5.3.15", "@types/react": "^18", "@types/react-dom": "^18", "@types/ua-parser-js": "^0.7.39", "cross-env": "^7.0.3", "esbuild": "^0.25.4", "remix-flat-routes": "^0.8.4", "rollup": "^4.34.5", "tsx": "^4.19.2", "typescript": "5.6.2", "vite": "^6.3.5", "vite-plugin-babel-macros": "^1.0.6", "vite-tsconfig-paths": "^5.1.4"}, "version": "1.11.1"}