name: 'CodeQL'

on:
  workflow_dispatch:
  push:
    branches: ['main']
  pull_request:
    branches: ['main']

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: true
      matrix:
        language: ['javascript']

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Copy env
        run: cp .env.example .env

      - uses: ./.github/actions/node-install

      - name: Build app
        run: npm run build

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
