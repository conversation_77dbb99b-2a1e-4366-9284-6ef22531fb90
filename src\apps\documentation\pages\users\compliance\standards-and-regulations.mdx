import { Callout } from 'nextra/components';

## 21 CFR Part 11

<Callout type="info" emoji="✅">
  Status: Compliant (Enterprise License)
</Callout>

21 CFR Part 11 is a regulation by the FDA that establishes the criteria for electronic records and electronic
signatures to ensure their authenticity, integrity, and confidentiality in the pharmaceutical, medical
device, and other FDA-regulated industries.

> Read more about 21 CFR Part 11 with Documenso here: https://documen.so/21-CFR-Part-11

### Main Requirements

- [x] Strong Identity Checks for each Signature
- [x] Signature and Audit Trails
- [x] User Access Management
- [x] Quality Assurance Documentation

## SOC/ SOC II

<Callout type="warning" emoji="⏳">
  Status: [Planned](https://github.com/documenso/backlog/issues/24)
</Callout>

SOC II is a framework for managing and auditing the security, availability, processing integrity, confidentiality,
and data privacy in cloud and IT service organizations, established by the American Institute of Certified
Public Accountants (AICPA).

## ISO 27001

<Callout type="warning" emoji="⏳">
  Status: [Planned](https://github.com/documenso/backlog/issues/26)
</Callout>
ISO 27001 is an international standard for managing information security, specifying requirements for
establishing, implementing, maintaining, and continually improving an information security management
system (ISMS).

### HIPAA

<Callout type="warning" emoji="⏳">
  Status: [Planned](https://github.com/documenso/backlog/issues/25)
</Callout>

The HIPAA (Health Insurance Portability and Accountability Act) is a U.S. law designed to protect patient health information's privacy and security and improve the healthcare system's efficiency and effectiveness.
