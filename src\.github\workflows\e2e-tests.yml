name: Playwright Tests
on:
  push:
    branches: ['main', 'feat/rr7']
  pull_request:
    branches: ['main']
jobs:
  e2e_tests:
    name: 'E2E Tests'
    timeout-minutes: 60
    runs-on: warp-ubuntu-2204-x64-16x
    steps:
      - uses: actions/checkout@v4

      - name: Copy env
        run: cp .env.example .env

      - uses: ./.github/actions/node-install

      - name: Start Services
        run: npm run dx:up

      - uses: ./.github/actions/playwright-install

      - name: Create the database
        run: npm run prisma:migrate-dev

      - name: Seed the database
        run: npm run prisma:seed

      - name: Build app
        run: npm run build

      - name: Install playwright browsers
        run: npx playwright install --with-deps

      - name: Run Playwright tests
        run: npm run ci
        env:
          # Needed since we use next start which will set the NODE_ENV to production
          NEXT_PRIVATE_SIGNING_LOCAL_FILE_PATH: './example/cert.p12'

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: 'packages/app-tests/**/test-results/*'
          retention-days: 30
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
