---
title: Fair Use Policy
description: Learn about our fair use policy, which enables us to have unlimited plans.
---

import { Callout } from 'nextra/components';

# Fair Use Policy

### Why

We offer our plans without any limits on volume because we want our users and customers to make the most of their accounts. Estimating volume is incredibly hard, especially for shorter intervals like a quarter. We are not interested in selling volume packages our customers end up not using. This is why the individual plan and the team plan do not include a limit on signing or API volume. If you are a customer of these [plans](https://documen.so/pricing), we ask you to abide by this fair use policy:

### Spirit of the Plan

> Use the limitless accounts as much as you like (they are meant to offer a lot) while respecting the spirit and intended scope of the account.

<Callout type="info">
  What happens if I violate this policy? We will ask you to upgrade to a fitting plan or custom
  pricing. We won’t block your account without reaching out. [Message
  us](mailto:<EMAIL>) for questions. It's probably fine, though.
</Callout>

### DO

- Sign as many documents with the individual plan for your single business or organization you are part of
- Use the API and Zapier to automate all your signing to sign as much as possible
- Experiment with the plans and integrations, testing what you want to build: When in doubt, do it. Especially if you are just starting.

### DON'T

- Use the individual account's API to power a platform
- Run a huge company, signing thousands of documents per day on a two-user team plan using the API
- Let this policy make you overthink. If you are a paying customer, we want you to win, and it's probably fine
