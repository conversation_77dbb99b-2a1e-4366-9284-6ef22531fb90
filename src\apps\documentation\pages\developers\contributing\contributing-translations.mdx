---
title: Contributing Translations
description: Learn how to contribute translations to Documenso and become part of our community.
---

import { Callout, Steps } from 'nextra/components';

# Contributing Translations

We are always open for help with translations! Currently we utilise AI to generate the initial translations for new languages, which are then improved over time by our awesome community.

If you are looking for development notes on translations, you can find them [here](/developers/local-development/translations).

<Callout type="info">
  Contributions are made through GitHub Pull Requests, so you will need a GitHub account to
  contribute.
</Callout>

## Overview

We store our translations in PO files, which are located in our GitHub repository [here](https://github.com/documenso/documenso/tree/main/packages/lib/translations).

The translation files are organized into folders represented by their respective language codes (`en` for English, `de` for German, etc).

Each PO file contains translations which look like this:

```po
#: apps/web/src/app/(signing)/sign/[token]/no-longer-available.tsx:61
msgid "Want to send slick signing links like this one? <0>Check out Documenso.</0>"
msgstr "Möchten Sie auffällige Signatur-Links wie diesen senden? <0>Überprüfen Sie Documenso.</0>"
```

- `msgid`: The original text in English (never edit this manually)
- `msgstr`: The translated text in the target language

<Callout type="warning">
  Notice the `<0>` tags? These represent HTML elements and must remain in both the `msgid` and `msgstr`. Make sure to translate the content between these tags while keeping the tags intact.
</Callout>

## How to Contribute

### Updating Existing Translations

1. Fork the repository.
2. Navigate to the appropriate language folder and open the PO file you want to update.
3. Make your changes, ensuring you follow the PO file format.
4. Commit your changes with a message such as `chore: update German translations`
5. Create a Pull Request.

### Adding a New Language

If you want to add translations for a language that doesn't exist yet:

1. Create an issue in our GitHub repository requesting the addition of the new language.
2. Wait for our team to review and approve the request.
3. Once approved, we will set up the necessary files and kickstart the translations with AI to provide initial coverage.

## Need Help?

<Callout type="info">
  If you have any questions, hop into our [Discord](https://documen.so/discord) and ask us directly!
</Callout>

Thank you for helping make Documenso more accessible to users around the world!
