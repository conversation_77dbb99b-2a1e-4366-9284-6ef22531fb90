---
title: Get Started
description: Learn how to use embedding to bring signing to your own website or application
---

# Embedding

Our embedding feature lets you integrate our document signing experience into your own application or website. Whether you're building with React, Preact, Vue, Svelte, Solid, Angular, or using generalized web components, this guide will help you get started with embedding Documenso.

## Availability

Embedding is currently available for all users on a **Teams Plan** and above, as well as **Early Adopter's** within a team (Early Adopters can create a team for free).

Our **Platform Plan** offers enhanced customization features including:

- Custom CSS and styling variables
- Dark mode controls
- The removal of Documenso branding from the embedding experience

## How Embedding Works

Embedding with Documenso allows you to handle document signing in two main ways:

1. **Using Direct Templates**: Using direct templates you can have an evergreen template that upon completion will create a new document within Documenso.
2. **Using a Signing Token**: A more advanced option for those running rich integrations with Documenso already. Given a recipients signing token you can embed the signing experience in your application rather than direct the recipient to Documenso.

_For most use-cases we recommend using direct templates, however if you have a need for a more advanced integration, we are happy to help you get started._

## Customization Options

### Styling and Theming

Platform customers have access to advanced styling options to customize the embedding experience:

1. **Custom CSS**: You can provide custom CSS to style the embedded component:

```jsx
<EmbedDirectTemplate
  token={token}
  css={`
    .documenso-embed {
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  `}
/>
```

2. **CSS Variables**: Fine-tune the appearance using CSS variables for colors, spacing, and more:

```jsx
<EmbedDirectTemplate
  token={token}
  cssVars={{
    primary: '#0000FF',
    background: '#F5F5F5',
    radius: '8px',
  }}
/>
```

For a complete list of available CSS variables and their usage, see our [CSS Variables](/developers/embedding/css-variables) documentation.

3. **Dark Mode Control**: Disable dark mode if it doesn't match your application's theme:

```jsx
<EmbedDirectTemplate token={token} darkModeDisabled={true} />
```

These customization options are available for both Direct Templates and Signing Token embeds.

## Supported Frameworks

We support embedding across a range of popular JavaScript frameworks, including:

| Framework | Package                                                                            |
| --------- | ---------------------------------------------------------------------------------- |
| React     | [@documenso/embed-react](https://www.npmjs.com/package/@documenso/embed-react)     |
| Preact    | [@documenso/embed-preact](https://www.npmjs.com/package/@documenso/embed-preact)   |
| Vue       | [@documenso/embed-vue](https://www.npmjs.com/package/@documenso/embed-vue)         |
| Svelte    | [@documenso/embed-svelte](https://www.npmjs.com/package/@documenso/embed-svelte)   |
| Solid     | [@documenso/embed-solid](https://www.npmjs.com/package/@documenso/embed-solid)     |
| Angular   | [@documenso/embed-angular](https://www.npmjs.com/package/@documenso/embed-angular) |

Additionally, we provide **web components** for more generalized use. However, please note that web components are still in their early stages and haven't been extensively tested.

## Embedding with Direct Templates

#### Instructions

To get started with embedding using a Direct Template we will need the URL segment which is also referred to as the token for the template.

You can find your URL/Token by performing the following steps:

1. **Navigate to your team's templates within Documenso**

![Team Templates](/embedding/team-templates.png)

2. **Click on the direct link template you want to embed**

This will copy the URL to your clipboard, e.g. `https://stg-app.documenso.com/d/-WoSwWVT-fYOERS2MI37k`

**For the above url the token is `-WoSwWVT-fYOERS2MI37k`**

3. Provide the token to the `EmbedDirectTemplate` component in your frameworks SDK

```jsx
import { EmbedDirectTemplate } from '@documenso/embed-react';

const MyEmbeddingComponent = () => {
  const token = 'YOUR_TOKEN_HERE'; // Replace with the actual token

  return <EmbedDirectTemplate token={token} />;
};
```

---

**Converting a regular template to a direct link template**

If you don't currently have any direct link templates you can easily create one by selecting the "Direct Link" option within the actions dropdown on the templates table.

This will show a dialog which will ask you to configure which recipient should be used as the direct link signer.

![Enable Direct Link Template](/embedding/enable-direct-link.png)

---

## Embedding with Signing Tokens

To embed the signing process for an ordinary document, you'll need a **document signing token** for the recipient. This token provides the necessary access to load the document and facilitate the signing process securely.

#### Instructions

1. Retrieve the signing token for the recipient document you want to embed

This will typically be done using an API integration where signing tokens are provided as part of the response when creating a document. Alternatively you can manually get a signing link by clicking hovering over a recipients avatar and clicking their email on a document that you own.

![Copy Recipient Token](/embedding/copy-recipient-token.png)

With the signing url on our clipboard we can extract the token the same way we did for the direct link template.

So `https://stg-app.documenso.com/sign/lm7Tp2_yhvFfzdeJQzYQF` will become `lm7Tp2_yhvFfzdeJQzYQF`

2. Provide the token to the `EmbedSignDocument` component in your frameworks SDK

```jsx
import { EmbedSignDocument } from '@documenso/embed-react';

const MyEmbeddingComponent = () => {
  const token = 'YOUR_TOKEN_HERE'; // Replace with the actual token

  return <EmbedSignDocument token={token} />;
};
```

---

## Using Embedding in Your Application

Once you've obtained the appropriate tokens, you can integrate the signing experience into your application. For framework-specific instructions, please refer to the guides provided in our documentation for:

- [React](/developers/embedding/react)
- [Preact](/developers/embedding/preact)
- [Vue](/developers/embedding/vue)
- [Svelte](/developers/embedding/svelte)
- [Solid](/developers/embedding/solid)
- [Angular](/developers/embedding/angular)

If you're using **web components**, the integration process is slightly different. Keep in mind that web components are currently less tested but can still provide flexibility for general use cases.

## Embedded Authoring

In addition to embedding signing experiences, Documenso now supports **embedded authoring**, allowing your users to create documents and templates directly within your application.

With embedded authoring, you can:

- Create new documents with custom fields
- Configure document properties and settings
- Set up recipients and signing workflows
- Customize the authoring experience

For detailed implementation instructions and code examples, see our [Embedded Authoring](/developers/embedding/authoring) guide.

## Related

- [React Integration](/developers/embedding/react)
- [Vue Integration](/developers/embedding/vue)
- [Svelte Integration](/developers/embedding/svelte)
- [Solid Integration](/developers/embedding/solid)
- [Preact Integration](/developers/embedding/preact)
- [Angular Integration](/developers/embedding/angular)
- [CSS Variables](/developers/embedding/css-variables)
- [Embedded Authoring](/developers/embedding/authoring)
