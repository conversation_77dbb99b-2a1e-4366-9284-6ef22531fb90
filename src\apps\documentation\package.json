{"name": "@documenso/documentation", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint:fix": "next lint --fix", "clean": "rimraf .next && rimraf node_modules"}, "dependencies": {"@documenso/assets": "*", "@documenso/lib": "*", "@documenso/tailwind-config": "*", "@documenso/trpc": "*", "@documenso/ui": "*", "next": "14.2.28", "next-plausible": "^3.12.0", "nextra": "^2.13.4", "nextra-theme-docs": "^2.13.4", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "5.6.2"}}