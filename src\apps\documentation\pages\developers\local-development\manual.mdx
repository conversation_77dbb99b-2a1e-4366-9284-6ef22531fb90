---
title: Manual Setup
description: Manually set up Documenso on your machine for local development.
---

import { Callout, Steps } from 'nextra/components';

# Manual Setup

Follow these steps to set up Documenso on your local machine:

<Steps>

### Fork Documenso

Fork the [Documenso repository](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/working-with-forks/about-forks) to your GitHub account.

### Clone Repository

After forking the repository, clone it to your local device by using the following command:

```bash
git clone https://github.com/<your-username>/documenso
```

### Install Dependencies

Run `npm i` in the root directory to install the dependencies required for the project.

### Set Up Environment Variables

Set up the following environment variables in the `.env` file:

```bash
NEXTAUTH_SECRET
NEXT_PUBLIC_WEBAPP_URL
NEXT_PRIVATE_DATABASE_URL
NEXT_PRIVATE_DIRECT_DATABASE_URL
NEXT_PRIVATE_SMTP_FROM_NAME
NEXT_PRIVATE_SMTP_FROM_ADDRESS
```

Alternatively, you can run `cp .env.example .env` to get started with our handpicked defaults.

### Create Database Schema

Create the database schema by running the following command:

```bash
npm run prisma:migrate-dev
```

### Optional: Seed the Database

Seed the database with test data by running the following command:

```bash
npm run prisma:seed -w @documenso/prisma
```

### Start the Application

Run `npm run dev` in the root directory to start the application.

### Access the Application

Access the Documenso application by visiting `http://localhost:3000` in your web browser.

</Steps>

<Callout type="info">
  Optional: Create your signing certificate. To generate your own using these steps and a Linux
  Terminal or Windows Subsystem for Linux (WSL), see **[Create your signing
  certificate](/developers/local-development/signing-certificate)**.
</Callout>
