---
title: Direct Link Signing
description: Create a shareable link for document signing.
---

import { Callout, Steps } from 'nextra/components';

# Direct Link Signing

Direct Link Signing allows you to create a shareable link for document signing, where recipients can fill in their information and sign directly. Once the recipients sign the document, they will get it in their email. Also, if they have an account, the document gets saved in their account.

<Steps>

### Select a Document

Identify the template you want to share and make signable with a direct link. Then click on the 3 dots on the right side of the template and select "Direct link".

![Template page in the Documenso dashboard](/direct-links/document-direct-link-documenso.webp)

### Enable Direct Links

Once you click on "Direct link", you will be greeted with a modal where you can learn how direct links work. After reading the information, click "Enable direct link" to proceed.

![Enable the document direct link in the Documenso dashboard](/direct-links/enable-document-direct-link-modal.webp)

### Select the Recipient

The next step is to select the recipient for the direct link. You can select an existing recipient or click the "Create one automatically" button to create a new direct link recipient.

![Choose the Direct Link recipient](/direct-links/choose-direct-link-recipient-documenso.webp)

### Send the Link

After selecting the recipient, you will get a direct link to share with the recipient. The format of the link is as follows:

```bash
https://app.documenso.com/d/<random-generated-string>
```

### Optional: Add Templates to Your Profile

You can add templates with direct links to your public [Documenso profile](/users/profile) to make them signable anytime.

![Choose the Direct Link recipient](/direct-links/documenso-profile.webp)

The image above shows a template with a direct link added to the user's profile.

</Steps>
