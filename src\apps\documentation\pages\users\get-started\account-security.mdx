---
title: Secure Your Account
description: Learn how to secure your Documenso account with Two-Factor Authentication (2FA) and Passkeys.
---

import { Callout, Steps } from 'nextra/components';

# Account Security

Documenso offers several security features to help you protect your account and documents. This guide will walk you through the steps to set up Two-Factor Authentication (2FA) and Passkeys for your account.

Two-factor authentication (2FA) and Passkeys are used for high-security and high-compliance signatures.

## Enable Two-Factor Authentication (2FA)

<Steps>

### Navigate to Security Settings

Navigate to your account's [security settings](https://app.documenso.com/settings/security). Here, you can manage your password and other security settings.

![A screenshot of the Documenso's security page in the user settings](/get-started-images/documenso-account-security-page.webp)

### Enable 2FA

Click the "Enable 2FA" button to start setting up Two-Factor Authentication. You will be presented with a QR code that you can scan with your 2FA app or a code that you can manually enter.

![A screenshot of the Documenso's security page in the user settings](/get-started-images/documenso-enable-2-factor-authentication.webp)

### Scan the QR Code

Use your 2FA app (e.g. Google Authenticator, Microsoft Authenticator) to scan the QR code. This will link your account to the 2FA app and generate a code that you can use to log in.

### Enter the 2FA Generated Code

After scanning the QR code, you will be prompted to enter the code generated by your 2FA app. After entering the code, click the "Enable 2FA" button to complete the process.

### 2FA Enabled

You have successfully enabled Two-Factor Authentication (2FA) for your account. To log in, you must enter the code generated by your 2FA app.

<Callout>
  Logging in with Google will not require a 2FA code. As an authentication provider, your Google
  account is considered secure. e.g. configuring 2FA for your Google account.
</Callout>

<Callout>
  Logging in using a passkey will also not require a 2FA code since passkeys are considered 2FA by
  design. The passkey itself is the first factor, and access to the device that holds it is
  considered the second factor.
</Callout>

### Extra: Save the Backup Codes

Be sure to download and safely store the 2FA backup codes in case you lose access to your 2FA app. You can use these codes to log in to your account.

</Steps>

## Add Passkeys

A passkey is like a secret password stored locally on your device. You can log in from the device it was created on but not from another device.

<Steps>

### Navigate to Security Settings

Navigate to the [security settings](https://app.documenso.com/settings/security) in your account.

### Manage Passkeys

Click the "Manage passkeys" button to start setting up a passkey. You will be taken to a new page where you can manage your passkeys or add a new one.

![A screenshot of the Documenso's passkeys page](/get-started-images/documenso-passkeys-page.webp)

### Add a New Passkey

To add a new passkey, click the "Add passkey" button. This opens a modal window where you can choose a passkey name.

![A screenshot of the "Add passkey" modal](/get-started-images/documenso-add-passkey-box.webp)

After entering the passkey name, click the "Continue" button to proceed.

What happens next depends on the passkey provider you have configured. If you have a passkey provider installed in your browser, you will be prompted to add the passkey there. If not, you will be prompted to add the passkey to your browser's passkey manager.

Whatever option you choose, follow the on-screen instructions to add the passkey. Once the passkey is added, you can use it to log in to your account.

### Manage Passkeys

You can manage your passkeys from the passkeys page. You can see the list of passkeys you have added and remove them if needed.

</Steps>
