---
title: Public API
description: Learn how to interact with your documents programmatically using the Documenso public API.
---

import { Callout, Steps } from 'nextra/components';

# Public API

Documenso provides a public REST API enabling you to interact with your documents programmatically. The API exposes various HTTP endpoints that allow you to perform operations such as:

- retrieving, uploading, deleting, and sending documents for signing
- creating, updating, and deleting recipients
- creating, updating, and deleting document fields

The documentation walks you through creating API keys and using them to authenticate your API requests. You'll also learn about the available endpoints, request and response formats, and how to use the API.

## API V1 - Stable

Check out the [API V1 documentation](https://app.documenso.com/api/v1/openapi) for details about the API endpoints, request parameters, response formats, and authentication methods.

## API V2 - Beta

<Callout type="warning">API V2 is currently beta, and will be subject to breaking changes</Callout>

Check out the [API V2 documentation](https://documen.so/api-v2-docs) for details about the API endpoints, request parameters, response formats, and authentication methods.

Our new API V2 supports the following typed SDKs:

- [TypeScript](https://github.com/documenso/sdk-typescript)
- [Python](https://github.com/documenso/sdk-python)
- [Go](https://github.com/documenso/sdk-go)

<Callout type="info">
  For the staging API, please use the following base URL:
  `https://stg-app.documenso.dev/api/v2-beta/`
</Callout>

🚀 [V2 Announcement](https://documen.so/sdk-blog)

📖 [Documentation](https://documen.so/api-v2-docs)

💬 [Leave Feedback](https://documen.so/sdk-feedback)

🔔 [Breaking Changes](https://documen.so/sdk-breaking)

## Availability

The API is available to individual users, teams and higher plans. [Fair Use](https://documen.so/fair) applies.
