---
title: API Versioning
description: Versioning information for the Documenso public API.
---

import { Callout } from 'nextra/components';

# API Versioning

Documenso uses API versioning to manage changes to the public API. This allows us to introduce new features, fix bugs, and make other changes without breaking existing integrations.

<Callout type="info">The current version of the API is `v1`.</Callout>

The API version is specified in the URL. For example, the base URL for the `v1` API is `https://app.documenso.com/api/v1`.

We may make changes to the API without incrementing the version number. We will always try to avoid breaking changes, but in some cases, it may be necessary to make changes that are not backward compatible. In these cases, we will increment the version number and provide information about the changes in the release notes.

Also, we may deprecate certain features or endpoints in the API. When we deprecate a feature or endpoint, we will provide information about the deprecation in the release notes and give a timeline for when the feature or endpoint will be removed.
