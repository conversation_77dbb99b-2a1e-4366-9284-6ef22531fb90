{"typescript.tsdk": "node_modules/typescript/lib", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "eslint.validate": ["typescript", "typescriptreact", "javascript", "javascriptreact"], "javascript.preferences.importModuleSpecifier": "non-relative", "javascript.preferences.useAliasesForRenames": false, "typescript.enablePromptUseWorkspaceTsdk": true, "files.eol": "\n", "editor.tabSize": 2, "editor.insertSpaces": true, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}