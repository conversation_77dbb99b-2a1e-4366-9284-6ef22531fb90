name: Auto Label Assigned Issues

on:
  issues:
    types: [assigned]

jobs:
  label-when-assigned:
    runs-on: ubuntu-latest
    steps:
      - name: Label issue
        uses: actions/github-script@v6
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            const issue = context.issue;
            // To run only on issues and not on PR
            if (github.context.payload.issue.pull_request === undefined) {
              const labelResponse = await github.rest.issues.addLabels({
                owner: issue.owner,
                repo: issue.repo,
                issue_number: issue.number,
                labels: ['status: assigned']
              });
            }
