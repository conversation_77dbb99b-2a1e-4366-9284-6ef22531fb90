name: 'Extract and upload translations'

on:
  workflow_dispatch:
  workflow_call:
  push:
    branches: ['main']

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  extract_translations:
    name: Extract and upload translations
    runs-on: ubuntu-latest
    environment: Translations
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GH_PAT }}

      - uses: ./.github/actions/node-install

      - name: Extract translations
        run: npm run translate:extract

      - name: Check and commit any files created
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '<EMAIL>'
          git add packages/lib/translations
          git diff --staged --quiet --exit-code || (git commit -m "chore: extract translations" && git push)

      - name: Compile translations
        id: compile_translations
        run: npm run translate:compile -- -- --strict
        continue-on-error: true

      - name: Upload missing translations
        if: ${{ steps.compile_translations.outcome == 'failure' }}
        uses: crowdin/github-action@v2
        with:
          upload_sources: true
          upload_translations: true
          download_translations: false
          localization_branch_name: chore/translations
        env:
          # A numeric ID, found at https://crowdin.com/project/<projectName>/tools/api
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}

          # Visit https://crowdin.com/settings#api-key to create this token
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
