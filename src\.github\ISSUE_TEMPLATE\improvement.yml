name: 'General Improvement Request'
description: 'Suggest a minor enhancement or improvement for this project'
title: '[Title for your improvement suggestion]'
body:
  - type: textarea
    attributes:
      label: 'Describe the improvement you are suggesting in detail'
      description: 'Explain why this improvement would be beneficial. Share any context, pain points, or reasons for suggesting this change.'
    validations:
      required: true
  - type: textarea
    id: description
    attributes:
      label: 'Additional Information & Alternatives (optional)'
      description: 'Are there any additional context or information that might be relevant to the improvement suggestion.'
    validations:
      required: false
  - type: dropdown
    id: assignee
    attributes:
      label: 'Do you want to work on this improvement?'
      multiple: false
      options:
        - 'No'
        - 'Yes'
      default: 0
    validations:
      required: true
  - type: checkboxes
    attributes:
      label: 'Please check the boxes that apply to this improvement suggestion.'
      options:
        - label: 'I have searched the existing issues and improvement suggestions to avoid duplication.'
        - label: 'I have provided a clear description of the improvement being suggested.'
        - label: 'I have explained the rationale behind this improvement.'
        - label: 'I have included any relevant technical details or design suggestions.'
        - label: 'I understand that this is a suggestion and that there is no guarantee of implementation.'
    validations:
      required: true
